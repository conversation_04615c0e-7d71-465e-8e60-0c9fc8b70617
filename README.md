# 刀刀乐捡漏网 - 微信小程序

一个专注于特价商品的微信小程序，支持零售和批发两种用户类型，提供商品浏览、搜索、购物车、下单、收藏等完整功能。

## 项目特色

- 🎯 **特价捡漏**：突出特价商品，批发价与零售价对比
- 👥 **双用户类型**：支持零售用户和批发用户，享受不同价格
- 🛒 **完整购物流程**：商品浏览 → 加入购物车 → 下单结算
- 📱 **原生小程序**：使用微信小程序原生技术栈开发
- 🔧 **Mock模式**：支持离线运行，便于开发调试

## 技术栈

- **前端框架**：微信小程序原生开发
- **文件结构**：.json/.wxml/.wxss/.js
- **网络请求**：封装wx.request，支持token自动注入
- **状态管理**：使用App全局状态管理
- **本地存储**：wx.storage存储用户信息和购物车

## 项目结构

```
├── app.js                 # 小程序入口文件
├── app.json              # 小程序配置文件
├── app.wxss              # 全局样式文件
├── pages/                # 页面目录
│   ├── index/            # 首页（商品列表）
│   ├── category/         # 分类页
│   ├── detail/           # 商品详情页
│   ├── cart/             # 购物车页
│   └── user/             # 用户页（登录/注册/个人中心）
├── utils/                # 工具类
│   └── request.js        # 网络请求封装
├── services/             # API服务层
│   └── api.js            # 接口封装
├── images/               # 图片资源（需要自行添加）
└── README.md             # 项目说明
```

## 快速开始

### 1. 导入项目

1. 下载并安装微信开发者工具
2. 打开微信开发者工具，选择"小程序"
3. 点击"新建项目"
4. 选择项目目录，输入AppID（测试可使用测试号）
5. 将本项目所有文件复制到项目目录
6. 点击"导入项目"即可运行

### 2. 立即体验（Mock模式）

项目默认开启Mock模式，可以立即体验所有功能：

1. **浏览商品**：首页展示商品列表，支持搜索和分类筛选
2. **查看详情**：点击商品进入详情页，可以收藏和加入购物车
3. **管理购物车**：购物车支持数量调整、价格模式切换
4. **用户注册登录**：支持零售用户和批发用户注册
5. **下单结算**：完整的下单流程体验

**测试账号**：
- 用户名：test
- 密码：123456

### 2. 配置说明

#### Mock模式配置

在 `app.js` 中可以配置Mock模式：

```javascript
globalData: {
  mock: true,  // true=使用Mock数据，false=调用真实接口
  baseUrl: 'https://api.daodaole.com'  // 真实API地址
}
```

#### 切换Mock模式

- 方法1：修改 `app.js` 中的 `mock` 字段
- 方法2：在小程序"我的"页面中点击"Mock模式"开关

### 3. 添加图片资源

项目需要以下图片资源，请放置在 `images/` 目录下：

```
images/
├── home.png              # 首页图标
├── home-active.png       # 首页图标（选中）
├── category.png          # 分类图标
├── category-active.png   # 分类图标（选中）
├── cart.png              # 购物车图标
├── cart-active.png       # 购物车图标（选中）
├── user.png              # 用户图标
├── user-active.png       # 用户图标（选中）
└── placeholder.png       # 商品占位图
```

**图标建议尺寸**：81px × 81px（3倍图）

#### 使用阿里Iconfont（推荐）

1. 访问 [iconfont.cn](https://www.iconfont.cn/)
2. 搜索并下载所需图标
3. 将图标保存为PNG格式，放入 `images/` 目录
4. 或者使用字体图标方式（需要额外配置）

## 功能说明

### 用户系统

- **登录/注册**：支持用户名密码登录
- **用户类型**：零售用户/批发用户
- **Token管理**：自动存储和注入Authorization头

### 商品系统

- **商品列表**：支持分页加载、搜索、分类筛选
- **商品详情**：展示详细信息、支持收藏、加入购物车
- **价格显示**：零售价/批发价对比展示

### 购物车系统

- **购物车管理**：增删改查、数量调整
- **价格切换**：批发用户可切换零售价/批发价
- **结算下单**：选择商品进行结算

### 收藏系统

- **商品收藏**：收藏/取消收藏商品
- **收藏列表**：查看收藏的商品

## API接口说明

### 接口基础信息

- **Base URL**：`https://api.daodaole.com`
- **认证方式**：Bearer Token
- **响应格式**：JSON

### 接口列表

#### 用户认证

```javascript
// 登录
POST /api/auth/login
Body: {"username":"test","password":"123456"}
Response: {"code":0,"message":"ok","data":{"token":"jwt_token","user":{"id":1,"username":"test","user_type":"retail"}}}

// 注册
POST /api/auth/register
Body: {"username":"abc","password":"123456","phone":"***********","user_type":"wholesale","business_license":"http://xxx.jpg"}
Response: {"code":0,"message":"ok"}
```

#### 商品管理

```javascript
// 商品列表
GET /api/products?category_id=1&keyword=手机&page=1&pageSize=10
Response: {"code":0,"message":"ok","data":{"list":[...],"total":100}}

// 商品详情
GET /api/products/:id
Response: {"code":0,"message":"ok","data":{"id":1,"name":"iPhone X",...}}
```

#### 分类管理

```javascript
// 分类列表
GET /api/categories
Response: {"code":0,"message":"ok","data":[{"id":1,"name":"手机"},...]}
```

#### 订单管理

```javascript
// 创建订单
POST /api/orders
Body: {"items":[{"product_id":1,"quantity":2}]}
Response: {"code":0,"message":"ok","data":{"order_id":101}}

// 订单列表
GET /api/orders?status=paid&page=1&pageSize=10
Response: {"code":0,"message":"ok","data":{"list":[...],"total":20}}
```

#### 收藏管理

```javascript
// 添加收藏
POST /api/favorites
Body: {"product_id":1}
Response: {"code":0,"message":"ok"}

// 收藏列表
GET /api/favorites
Response: {"code":0,"message":"ok","data":[...]}

// 删除收藏
DELETE /api/favorites/:id
Response: {"code":0,"message":"ok"}
```

## 本地存储说明

### Storage Keys

- `token`：用户登录token
- `user`：用户信息对象
- `cart`：购物车商品数组

### 数据结构

```javascript
// 用户信息
{
  "id": 1,
  "username": "test",
  "user_type": "retail" // retail=零售用户, wholesale=批发用户
}

// 购物车商品
[
  {
    "id": 1,
    "name": "iPhone X",
    "price_retail": 3000,
    "price_wholesale": 2800,
    "image": "http://...",
    "quantity": 2
  }
]
```

## 开发说明

### 网络请求

所有API调用都通过 `services/api.js` 进行，自动处理：

- Token注入
- 错误处理
- Loading状态
- Mock数据返回

### 状态管理

使用App全局状态管理用户信息和购物车：

```javascript
const app = getApp()

// 获取用户信息
app.globalData.userInfo

// 添加商品到购物车
app.addToCart(product)

// 获取购物车数量
app.getCartCount()
```

### 页面生命周期

- `onLoad`：页面加载时初始化数据
- `onShow`：页面显示时刷新状态（如购物车数量）
- `onPullDownRefresh`：下拉刷新数据

## 部署说明

### 开发环境

1. 确保Mock模式开启（`app.globalData.mock = true`）
2. 在微信开发者工具中预览和调试

### 生产环境

1. 关闭Mock模式（`app.globalData.mock = false`）
2. 配置正确的API地址（`app.globalData.baseUrl`）
3. 上传代码到微信小程序后台
4. 提交审核并发布

## 注意事项

1. **图片资源**：请确保添加所有必需的图标文件
2. **API接口**：Mock模式下使用本地数据，生产环境需要真实后端接口
3. **用户体验**：建议在真机上测试完整流程
4. **性能优化**：商品图片建议使用CDN，并开启懒加载

## 技术支持

如有问题，请检查：

1. 微信开发者工具版本是否最新
2. 项目文件是否完整
3. Mock模式配置是否正确
4. 控制台是否有错误信息

---

**刀刀乐捡漏网** - 让每一次购物都是捡漏的快乐！ 🎉
