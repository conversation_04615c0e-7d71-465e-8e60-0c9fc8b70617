// API服务层 - 封装所有后端接口调用
const { get, post, put, delete: del } = require('../utils/request')

/**
 * 用户模块API
 */
const authAPI = {
  /**
   * 用户登录
   * @param {Object} data 登录数据
   * @param {string} data.username 用户名
   * @param {string} data.password 密码
   * @returns {Promise} 返回用户信息和token
   */
  login(data) {
    return post('/api/auth/login', data, {
      loading: true,
      loadingText: '登录中...'
    })
  },

  /**
   * 用户注册
   * @param {Object} data 注册数据
   * @param {string} data.username 用户名
   * @param {string} data.password 密码
   * @param {string} data.phone 手机号
   * @param {string} data.user_type 用户类型 retail/wholesale
   * @param {string} data.business_license 营业执照(批发用户)
   * @returns {Promise}
   */
  register(data) {
    return post('/api/auth/register', data, {
      loading: true,
      loadingText: '注册中...'
    })
  }
}

/**
 * 商品模块API
 */
const productAPI = {
  /**
   * 获取商品列表
   * @param {Object} params 查询参数
   * @param {number} params.category_id 分类ID
   * @param {string} params.keyword 搜索关键字
   * @param {number} params.page 页码
   * @param {number} params.pageSize 每页数量
   * @returns {Promise} 返回商品列表和总数
   */
  fetchProducts(params = {}) {
    const defaultParams = {
      page: 1,
      pageSize: 10
    }
    return get('/api/products', { ...defaultParams, ...params })
  },

  /**
   * 获取商品详情
   * @param {number} id 商品ID
   * @returns {Promise} 返回商品详情
   */
  fetchProductDetail(id) {
    return get(`/api/products/${id}`, {}, {
      loading: true,
      loadingText: '加载中...'
    })
  }
}

/**
 * 分类模块API
 */
const categoryAPI = {
  /**
   * 获取分类列表
   * @returns {Promise} 返回分类列表
   */
  fetchCategories() {
    return get('/api/categories')
  }
}

/**
 * 订单模块API
 */
const orderAPI = {
  /**
   * 创建订单
   * @param {Object} data 订单数据
   * @param {Array} data.items 商品列表
   * @param {number} data.items[].product_id 商品ID
   * @param {number} data.items[].quantity 数量
   * @returns {Promise} 返回订单ID
   */
  createOrder(data) {
    return post('/api/orders', data, {
      loading: true,
      loadingText: '提交订单中...'
    })
  },

  /**
   * 获取订单列表
   * @param {Object} params 查询参数
   * @param {string} params.status 订单状态
   * @param {number} params.page 页码
   * @param {number} params.pageSize 每页数量
   * @returns {Promise} 返回订单列表
   */
  fetchOrders(params = {}) {
    const defaultParams = {
      page: 1,
      pageSize: 10
    }
    return get('/api/orders', { ...defaultParams, ...params })
  },

  /**
   * 获取订单详情
   * @param {number} id 订单ID
   * @returns {Promise} 返回订单详情
   */
  fetchOrderDetail(id) {
    return get(`/api/orders/${id}`, {}, {
      loading: true,
      loadingText: '加载中...'
    })
  }
}

/**
 * 收藏模块API
 */
const favoriteAPI = {
  /**
   * 添加收藏
   * @param {Object} data 收藏数据
   * @param {number} data.product_id 商品ID
   * @returns {Promise}
   */
  addFavorite(data) {
    return post('/api/favorites', data, {
      loading: true,
      loadingText: '收藏中...'
    })
  },

  /**
   * 获取收藏列表
   * @returns {Promise} 返回收藏列表
   */
  fetchFavorites() {
    return get('/api/favorites')
  },

  /**
   * 删除收藏
   * @param {number} id 收藏ID
   * @returns {Promise}
   */
  deleteFavorite(id) {
    return del(`/api/favorites/${id}`, {}, {
      loading: true,
      loadingText: '取消收藏中...'
    })
  }
}

// 导出所有API
module.exports = {
  authAPI,
  productAPI,
  categoryAPI,
  orderAPI,
  favoriteAPI
}

/**
 * 使用示例：
 * 
 * // 在页面中引入
 * const { productAPI, authAPI } = require('../../services/api')
 * 
 * // 获取商品列表
 * productAPI.fetchProducts({ page: 1, pageSize: 10 })
 *   .then(res => {
 *     console.log('商品列表:', res.data.list)
 *     this.setData({
 *       products: res.data.list,
 *       total: res.data.total
 *     })
 *   })
 *   .catch(err => {
 *     console.error('获取商品列表失败:', err)
 *   })
 * 
 * // 用户登录
 * authAPI.login({ username: 'test', password: '123456' })
 *   .then(res => {
 *     console.log('登录成功:', res.data)
 *     const app = getApp()
 *     app.setUserInfo(res.data.user, res.data.token)
 *   })
 *   .catch(err => {
 *     console.error('登录失败:', err)
 *   })
 */
