/* 购物车页面样式 */

.page-container {
  padding-bottom: 120rpx; /* 为底部结算栏留出空间 */
  background-color: #f5f5f5;
}

/* 用户类型切换 */
.user-type-switch {
  background-color: white;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.switch-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.switch-label {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.switch-options {
  display: flex;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  padding: 4rpx;
}

.switch-option {
  padding: 12rpx 30rpx;
  border-radius: 26rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
}

.switch-option.active {
  background-color: #ff6b35;
  color: white;
}

/* 购物车内容 */
.cart-content {
  flex: 1;
}

/* 空购物车 */
.empty-cart {
  text-align: center;
  padding: 200rpx 40rpx;
  background-color: white;
  margin: 20rpx;
  border-radius: 12rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: bold;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 40rpx;
}

/* 全选栏 */
.select-all-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: white;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  margin: 0 20rpx 20rpx;
}

.select-all {
  display: flex;
  align-items: center;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15rpx;
  transition: all 0.3s ease;
}

.checkbox.checked {
  background-color: #ff6b35;
  border-color: #ff6b35;
}

.checkbox-icon {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.select-all-text {
  font-size: 28rpx;
  color: #333;
}

.clear-cart {
  font-size: 26rpx;
  color: #999;
  padding: 10rpx 20rpx;
}

/* 购物车商品列表 */
.cart-list {
  padding: 0 20rpx;
}

.cart-item {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 选择框 */
.item-select {
  margin-right: 20rpx;
}

/* 商品信息 */
.item-info {
  flex: 1;
  display: flex;
  align-items: center;
}

.item-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  object-fit: cover;
  margin-right: 20rpx;
}

.item-details {
  flex: 1;
}

.item-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 价格显示 */
.item-price {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.current-price {
  font-size: 30rpx;
  color: #ff6b35;
  font-weight: bold;
  margin-right: 15rpx;
}

.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}

/* 数量控制 */
.quantity-control {
  display: flex;
  align-items: center;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  border: 1rpx solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #666;
  background-color: #f5f5f5;
  transition: all 0.3s ease;
}

.quantity-btn:first-child {
  border-radius: 8rpx 0 0 8rpx;
}

.quantity-btn:last-child {
  border-radius: 0 8rpx 8rpx 0;
}

.quantity-btn:active {
  background-color: #e5e5e5;
}

.quantity-btn[disabled] {
  color: #ccc;
  background-color: #f9f9f9;
}

.quantity-input {
  width: 80rpx;
  height: 60rpx;
  border: 1rpx solid #ddd;
  border-left: none;
  border-right: none;
  text-align: center;
  font-size: 28rpx;
  background-color: white;
}

/* 删除按钮 */
.item-delete {
  margin-left: 20rpx;
  padding: 10rpx;
}

.delete-icon {
  font-size: 32rpx;
}

/* 底部结算栏 */
.checkout-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-top: 1rpx solid #e5e5e5;
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.checkout-info {
  flex: 1;
}

.selected-count {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.total-price {
  display: flex;
  align-items: center;
  margin-bottom: 5rpx;
}

.total-label {
  font-size: 26rpx;
  color: #333;
  margin-right: 10rpx;
}

.total-amount {
  font-size: 36rpx;
  color: #ff6b35;
  font-weight: bold;
}

.save-amount {
  font-size: 22rpx;
  color: #52c41a;
}

.checkout-btn {
  background-color: #ccc;
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: bold;
  min-width: 160rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.checkout-btn.active {
  background-color: #ff6b35;
}

.checkout-btn.active:active {
  background-color: #e55a2b;
}
