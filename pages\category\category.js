// 分类页面
const { productAPI, categoryAPI } = require('../../services/api')
const app = getApp()

Page({
  data: {
    // 分类列表
    categories: [],
    selectedCategoryId: 1, // 默认选中第一个分类
    
    // 商品列表
    products: [],
    
    // 分页相关
    page: 1,
    pageSize: 20,
    total: 0,
    hasMore: true,
    
    // 加载状态
    loading: false,
    loadingMore: false
  },

  onLoad: function (options) {
    console.log('分类页面加载')
    this.loadCategories()
  },

  onShow: function () {
    // 每次显示页面时刷新购物车数量
    this.updateCartCount()
  },

  onPullDownRefresh: function () {
    console.log('下拉刷新')
    this.onRefresh()
  },

  onReachBottom: function () {
    console.log('触底加载更多')
    this.loadMore()
  },

  // 刷新数据
  onRefresh: function () {
    this.setData({
      page: 1,
      products: [],
      hasMore: true
    })
    this.loadProducts()
    wx.stopPullDownRefresh()
  },

  // 加载分类列表
  loadCategories: function () {
    categoryAPI.fetchCategories()
      .then(res => {
        const categories = res.data || []
        this.setData({
          categories
        })
        
        // 如果有分类，默认选中第一个并加载商品
        if (categories.length > 0) {
          this.setData({
            selectedCategoryId: categories[0].id
          })
          this.loadProducts()
        }
      })
      .catch(err => {
        console.error('加载分类失败:', err)
      })
  },

  // 加载商品列表
  loadProducts: function (isLoadMore = false) {
    if (this.data.loading || this.data.loadingMore) {
      return
    }

    if (!this.data.selectedCategoryId) {
      return
    }

    this.setData({
      loading: !isLoadMore,
      loadingMore: isLoadMore
    })

    const params = {
      category_id: this.data.selectedCategoryId,
      page: this.data.page,
      pageSize: this.data.pageSize
    }

    console.log('加载分类商品:', params)

    productAPI.fetchProducts(params)
      .then(res => {
        const { list = [], total = 0 } = res.data || {}
        
        let products = []
        if (isLoadMore) {
          products = [...this.data.products, ...list]
        } else {
          products = list
        }

        const hasMore = products.length < total

        this.setData({
          products,
          total,
          hasMore,
          loading: false,
          loadingMore: false
        })

        console.log('分类商品加载完成:', {
          categoryId: this.data.selectedCategoryId,
          count: products.length,
          total,
          hasMore
        })
      })
      .catch(err => {
        console.error('加载分类商品失败:', err)
        this.setData({
          loading: false,
          loadingMore: false
        })
      })
  },

  // 加载更多
  loadMore: function () {
    if (!this.data.hasMore || this.data.loadingMore) {
      return
    }

    this.setData({
      page: this.data.page + 1
    })
    this.loadProducts(true)
  },

  // 分类选择
  onCategorySelect: function (e) {
    const categoryId = parseInt(e.currentTarget.dataset.id)
    console.log('选择分类:', categoryId)
    
    if (categoryId === this.data.selectedCategoryId) {
      return
    }
    
    this.setData({
      selectedCategoryId: categoryId,
      page: 1,
      products: [],
      hasMore: true
    })
    this.loadProducts()
  },

  // 跳转商品详情
  goToDetail: function (e) {
    const productId = e.currentTarget.dataset.id
    console.log('跳转商品详情:', productId)
    
    wx.navigateTo({
      url: `/pages/detail/detail?id=${productId}`
    })
  },

  // 加入购物车
  addToCart: function (e) {
    const product = e.currentTarget.dataset.product
    console.log('加入购物车:', product)
    
    if (!product) {
      wx.showToast({
        title: '商品信息错误',
        icon: 'none'
      })
      return
    }

    // 检查库存
    if (product.stock <= 0) {
      wx.showToast({
        title: '商品已售罄',
        icon: 'none'
      })
      return
    }

    // 添加到购物车
    app.addToCart(product)
    this.updateCartCount()
  },

  // 更新购物车数量
  updateCartCount: function () {
    const count = app.getCartCount()
    if (count > 0) {
      wx.setTabBarBadge({
        index: 2, // 购物车tab的索引
        text: count.toString()
      })
    } else {
      wx.removeTabBarBadge({
        index: 2
      })
    }
  }
})
