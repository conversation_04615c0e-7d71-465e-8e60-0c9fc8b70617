// 网络请求封装 - 支持token注入、mock模式、统一错误处理
const app = getApp()

// Mock数据
const mockData = {
  // 用户登录
  '/api/auth/login': {
    code: 0,
    message: 'ok',
    data: {
      token: 'mock_jwt_token_here_' + Date.now(),
      user: {
        id: 1,
        username: 'test',
        user_type: 'retail'
      }
    }
  },

  // 用户注册
  '/api/auth/register': {
    code: 0,
    message: 'ok'
  },

  // 商品列表
  '/api/products': {
    code: 0,
    message: 'ok',
    data: {
      list: [
        {
          id: 1,
          name: 'iPhone 14 Pro Max',
          price_retail: 8999,
          price_wholesale: 8500,
          stock: 10,
          image: 'https://via.placeholder.com/300x300?text=iPhone',
          category_id: 1
        },
        {
          id: 2,
          name: 'MacBook Pro 16寸',
          price_retail: 18999,
          price_wholesale: 17800,
          stock: 5,
          image: 'https://via.placeholder.com/300x300?text=MacBook',
          category_id: 2
        },
        {
          id: 3,
          name: 'AirPods Pro 2',
          price_retail: 1899,
          price_wholesale: 1699,
          stock: 20,
          image: 'https://via.placeholder.com/300x300?text=AirPods',
          category_id: 3
        }
      ],
      total: 100
    }
  },

  // 商品详情 - 支持动态ID
  '/api/products/1': {
    code: 0,
    message: 'ok',
    data: {
      id: 1,
      name: 'iPhone 14 Pro Max',
      description: '苹果最新旗舰手机，搭载A16仿生芯片，支持灵动岛设计。6.7英寸超视网膜XDR显示屏，ProMotion自适应刷新率技术，1200万像素三摄系统。',
      price_retail: 8999,
      price_wholesale: 8500,
      stock: 10,
      category_id: 1,
      image: 'https://via.placeholder.com/300x300?text=iPhone14',
      images: [
        'https://via.placeholder.com/300x300?text=iPhone14-1',
        'https://via.placeholder.com/300x300?text=iPhone14-2',
        'https://via.placeholder.com/300x300?text=iPhone14-3'
      ]
    }
  },
  '/api/products/2': {
    code: 0,
    message: 'ok',
    data: {
      id: 2,
      name: 'MacBook Pro 16寸',
      description: 'Apple M2 Pro芯片，16英寸Liquid Retina XDR显示屏，专业级性能，适合开发者和创意工作者。512GB SSD存储，16GB统一内存。',
      price_retail: 18999,
      price_wholesale: 17800,
      stock: 5,
      category_id: 2,
      image: 'https://via.placeholder.com/300x300?text=MacBook',
      images: [
        'https://via.placeholder.com/300x300?text=MacBook-1',
        'https://via.placeholder.com/300x300?text=MacBook-2'
      ]
    }
  },
  '/api/products/3': {
    code: 0,
    message: 'ok',
    data: {
      id: 3,
      name: 'AirPods Pro 2',
      description: '第二代AirPods Pro，主动降噪技术升级，空间音频，无线充电盒，续航时间更长。完美适配所有Apple设备。',
      price_retail: 1899,
      price_wholesale: 1699,
      stock: 20,
      category_id: 3,
      image: 'https://via.placeholder.com/300x300?text=AirPods',
      images: [
        'https://via.placeholder.com/300x300?text=AirPods-1',
        'https://via.placeholder.com/300x300?text=AirPods-2'
      ]
    }
  },

  // 分类列表
  '/api/categories': {
    code: 0,
    message: 'ok',
    data: [
      { id: 1, name: '手机' },
      { id: 2, name: '电脑' },
      { id: 3, name: '数码配件' },
      { id: 4, name: '家电' }
    ]
  },

  // 创建订单
  '/api/orders': {
    code: 0,
    message: 'ok',
    data: {
      order_id: 101
    }
  },

  // 订单列表
  '/api/orders': {
    code: 0,
    message: 'ok',
    data: {
      list: [
        {
          id: 101,
          total_price: 8999,
          status: 'pending',
          createdAt: '2025-08-17'
        }
      ],
      total: 20
    }
  },

  // 订单详情
  '/api/orders/101': {
    code: 0,
    message: 'ok',
    data: {
      id: 101,
      total_price: 8999,
      status: 'pending',
      items: [
        {
          product_id: 1,
          name: 'iPhone 14 Pro Max',
          price: 8999,
          quantity: 1
        }
      ]
    }
  },

  // 添加收藏
  '/api/favorites': {
    code: 0,
    message: 'ok'
  },

  // 收藏列表
  '/api/favorites': {
    code: 0,
    message: 'ok',
    data: [
      {
        id: 1,
        product_id: 1,
        product: {
          name: 'iPhone 14 Pro Max',
          price_retail: 8999,
          image: 'https://via.placeholder.com/300x300?text=iPhone'
        }
      }
    ]
  },

  // 删除收藏
  '/api/favorites/1': {
    code: 0,
    message: 'ok'
  }
}

/**
 * 网络请求封装
 * @param {Object} options 请求配置
 * @param {string} options.url 请求地址
 * @param {string} options.method 请求方法 GET/POST/PUT/DELETE
 * @param {Object} options.data 请求数据
 * @param {Object} options.header 请求头
 * @param {boolean} options.loading 是否显示加载提示
 * @param {string} options.loadingText 加载提示文字
 * @returns {Promise}
 */
function request(options = {}) {
  return new Promise((resolve, reject) => {
    const {
      url,
      method = 'GET',
      data = {},
      header = {},
      loading = false,
      loadingText = '加载中...'
    } = options

    // 显示加载提示
    if (loading) {
      wx.showLoading({
        title: loadingText,
        mask: true
      })
    }

    // Mock模式处理
    if (app.globalData.mock) {
      setTimeout(() => {
        if (loading) {
          wx.hideLoading()
        }

        // 根据URL和方法返回对应的mock数据
        let mockKey = url.split('?')[0] // 去掉查询参数
        let mockResponse = mockData[mockKey]

        // 处理动态路径，如 /api/products/123
        if (!mockResponse && mockKey.includes('/api/products/')) {
          const productId = mockKey.split('/api/products/')[1]
          if (productId && !isNaN(productId)) {
            // 如果有对应ID的mock数据就使用，否则使用默认的商品1数据
            mockResponse = mockData[`/api/products/${productId}`] || mockData['/api/products/1']
            if (mockResponse && mockResponse.data) {
              // 更新返回数据中的ID
              mockResponse = {
                ...mockResponse,
                data: {
                  ...mockResponse.data,
                  id: parseInt(productId)
                }
              }
            }
          }
        }

        if (mockResponse) {
          console.log('Mock响应:', mockKey, mockResponse)
          resolve(mockResponse)
        } else {
          console.warn('未找到Mock数据:', mockKey)
          resolve({
            code: 0,
            message: 'ok',
            data: null
          })
        }
      }, 500) // 模拟网络延迟
      return
    }

    // 构建完整URL
    const fullUrl = url.startsWith('http') ? url : app.globalData.baseUrl + url

    // 构建请求头
    const requestHeader = {
      'Content-Type': 'application/json',
      ...header
    }

    // 注入token
    if (app.globalData.token) {
      requestHeader['Authorization'] = `Bearer ${app.globalData.token}`
    }

    console.log('发起请求:', {
      url: fullUrl,
      method,
      data,
      header: requestHeader
    })

    // 发起请求
    wx.request({
      url: fullUrl,
      method,
      data,
      header: requestHeader,
      success: (res) => {
        console.log('请求成功:', res)
        
        if (loading) {
          wx.hideLoading()
        }

        // 统一处理响应
        const { statusCode, data: responseData } = res
        
        if (statusCode === 200) {
          // 检查业务状态码
          if (responseData.code === 0) {
            resolve(responseData)
          } else {
            // 业务错误
            const errorMsg = responseData.message || '请求失败'
            wx.showToast({
              title: errorMsg,
              icon: 'none',
              duration: 2000
            })
            reject(new Error(errorMsg))
          }
        } else if (statusCode === 401) {
          // token过期或无效，清除用户信息并跳转登录
          app.clearUserInfo()
          wx.showToast({
            title: '请先登录',
            icon: 'none'
          })
          setTimeout(() => {
            wx.switchTab({
              url: '/pages/user/user'
            })
          }, 1500)
          reject(new Error('未授权'))
        } else {
          // HTTP错误
          const errorMsg = `请求失败 (${statusCode})`
          wx.showToast({
            title: errorMsg,
            icon: 'none'
          })
          reject(new Error(errorMsg))
        }
      },
      fail: (err) => {
        console.error('请求失败:', err)
        
        if (loading) {
          wx.hideLoading()
        }

        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
        reject(err)
      }
    })
  })
}

// 快捷方法
const get = (url, data = {}, options = {}) => {
  const query = Object.keys(data).map(key => `${key}=${encodeURIComponent(data[key])}`).join('&')
  const fullUrl = query ? `${url}?${query}` : url
  
  return request({
    url: fullUrl,
    method: 'GET',
    ...options
  })
}

const post = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  })
}

const put = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  })
}

const del = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'DELETE',
    data,
    ...options
  })
}

module.exports = {
  request,
  get,
  post,
  put,
  delete: del
}
