/* 商品详情页样式 */

.page-container {
  padding-bottom: 120rpx; /* 为底部操作栏留出空间 */
}

/* 商品图片轮播 */
.product-images {
  position: relative;
  background-color: white;
  margin-bottom: 20rpx;
}

.image-swiper {
  width: 100%;
  height: 600rpx;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.discount-badge {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  background: linear-gradient(45deg, #ff6b35, #ff8c42);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.3);
  z-index: 10;
}

/* 商品基本信息 */
.product-basic-info {
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.product-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 20rpx;
}

/* 价格区域 */
.price-section {
  margin-bottom: 20rpx;
}

.price-row {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.price-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 15rpx;
  min-width: 80rpx;
}

.price-retail {
  font-size: 40rpx;
  font-weight: bold;
  color: #ff6b35;
  margin-right: 20rpx;
}

.price-wholesale {
  font-size: 32rpx;
  font-weight: bold;
  color: #52c41a;
  margin-right: 15rpx;
}

.save-amount {
  font-size: 24rpx;
  color: #52c41a;
  background-color: #f6ffed;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  border: 1rpx solid #b7eb8f;
}

/* 库存信息 */
.stock-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.stock-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
}

.stock-value {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-right: 15rpx;
}

.stock-value.low-stock {
  color: #ff6b35;
}

.low-stock-tip {
  font-size: 24rpx;
  color: #ff6b35;
  background-color: #fff2e8;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-left: 10rpx;
}

.out-of-stock {
  font-size: 24rpx;
  color: #f5222d;
  background-color: #fff1f0;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

/* 商品描述 */
.product-description {
  margin-bottom: 20rpx;
}

.description-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 用户类型提示 */
.user-type-tip {
  margin-bottom: 20rpx;
  background: linear-gradient(135deg, #fff7e6, #fff2e8);
  border: 1rpx solid #ffd591;
}

.tip-content {
  display: flex;
  align-items: center;
  padding: 20rpx;
}

.tip-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.tip-text {
  font-size: 26rpx;
  color: #d46b08;
  flex: 1;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 200rpx 40rpx;
  color: #999;
  font-size: 28rpx;
}

/* 错误状态 */
.error-state {
  text-align: center;
  padding: 200rpx 40rpx;
  color: #999;
}

.error-icon {
  font-size: 100rpx;
  margin-bottom: 20rpx;
}

.error-text {
  font-size: 28rpx;
  margin-bottom: 40rpx;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-top: 1rpx solid #e5e5e5;
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.action-left {
  display: flex;
  align-items: center;
}

.action-btn {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 40rpx;
  padding: 10rpx;
  transition: transform 0.2s ease;
}

.action-btn:active {
  transform: scale(0.95);
}

.action-icon {
  font-size: 40rpx;
  margin-bottom: 5rpx;
}

.action-text {
  font-size: 20rpx;
  color: #666;
}

.cart-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #ff6b35;
  color: white;
  font-size: 18rpx;
  padding: 2rpx 8rpx;
  border-radius: 20rpx;
  min-width: 30rpx;
  text-align: center;
  transform: translate(50%, -50%);
}

.action-right {
  display: flex;
  gap: 20rpx;
}

.action-right .btn {
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  border-radius: 50rpx;
  min-width: 160rpx;
}

.action-right .btn[disabled] {
  background-color: #f5f5f5;
  color: #ccc;
  border-color: #f5f5f5;
}
