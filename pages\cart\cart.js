// 购物车页面
const { orderAPI } = require('../../services/api')
const app = getApp()

Page({
  data: {
    // 用户信息
    userInfo: null,
    
    // 价格模式 retail/wholesale
    priceMode: 'retail',
    
    // 购物车商品
    cartItems: [],
    
    // 选择状态
    allSelected: false,
    selectedCount: 0,
    
    // 价格计算
    totalAmount: 0,
    saveAmount: 0
  },

  onLoad: function (options) {
    console.log('购物车页面加载')
  },

  onShow: function () {
    // 每次显示页面时刷新数据
    this.setData({
      userInfo: app.globalData.userInfo
    })
    this.initPriceMode()
    this.loadCartItems()
  },

  // 初始化价格模式
  initPriceMode: function () {
    const userInfo = this.data.userInfo
    let priceMode = 'retail'
    
    // 批发用户默认使用批发价
    if (userInfo && userInfo.user_type === 'wholesale') {
      priceMode = 'wholesale'
    }
    
    this.setData({ priceMode })
  },

  // 加载购物车商品
  loadCartItems: function () {
    const cart = app.globalData.cart || []
    
    // 为每个商品添加选择状态
    const cartItems = cart.map(item => ({
      ...item,
      selected: true // 默认全选
    }))
    
    this.setData({
      cartItems,
      allSelected: cartItems.length > 0
    })
    
    this.calculateTotal()
    this.updateTabBarBadge()
  },

  // 切换价格模式
  switchPriceMode: function (e) {
    const mode = e.currentTarget.dataset.mode
    console.log('切换价格模式:', mode)
    
    this.setData({
      priceMode: mode
    })
    this.calculateTotal()
  },

  // 全选/取消全选
  toggleSelectAll: function () {
    const allSelected = !this.data.allSelected
    const cartItems = this.data.cartItems.map(item => ({
      ...item,
      selected: allSelected
    }))
    
    this.setData({
      allSelected,
      cartItems
    })
    
    this.calculateTotal()
  },

  // 切换单个商品选择状态
  toggleItemSelect: function (e) {
    const index = e.currentTarget.dataset.index
    const cartItems = [...this.data.cartItems]
    
    cartItems[index].selected = !cartItems[index].selected
    
    // 检查是否全选
    const allSelected = cartItems.every(item => item.selected)
    
    this.setData({
      cartItems,
      allSelected
    })
    
    this.calculateTotal()
  },

  // 增加数量
  increaseQuantity: function (e) {
    const index = e.currentTarget.dataset.index
    const cartItems = [...this.data.cartItems]
    
    cartItems[index].quantity += 1
    
    this.setData({ cartItems })
    this.updateCart()
    this.calculateTotal()
  },

  // 减少数量
  decreaseQuantity: function (e) {
    const index = e.currentTarget.dataset.index
    const cartItems = [...this.data.cartItems]
    
    if (cartItems[index].quantity > 1) {
      cartItems[index].quantity -= 1
      
      this.setData({ cartItems })
      this.updateCart()
      this.calculateTotal()
    }
  },

  // 数量输入
  onQuantityInput: function (e) {
    const index = e.currentTarget.dataset.index
    const value = parseInt(e.detail.value) || 1
    const cartItems = [...this.data.cartItems]
    
    cartItems[index].quantity = Math.max(1, value)
    
    this.setData({ cartItems })
    this.updateCart()
    this.calculateTotal()
  },

  // 删除商品
  removeItem: function (e) {
    const index = e.currentTarget.dataset.index
    const item = this.data.cartItems[index]
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除"${item.name}"吗？`,
      success: (res) => {
        if (res.confirm) {
          const cartItems = [...this.data.cartItems]
          cartItems.splice(index, 1)
          
          this.setData({
            cartItems,
            allSelected: cartItems.length > 0 && cartItems.every(item => item.selected)
          })
          
          this.updateCart()
          this.calculateTotal()
          this.updateTabBarBadge()
        }
      }
    })
  },

  // 清空购物车
  clearCart: function () {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空购物车吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            cartItems: [],
            allSelected: false
          })
          
          app.clearCart()
          this.calculateTotal()
          this.updateTabBarBadge()
        }
      }
    })
  },

  // 跳转商品详情
  goToDetail: function (e) {
    const productId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/detail/detail?id=${productId}`
    })
  },

  // 去购物
  goShopping: function () {
    wx.switchTab({
      url: '/pages/index/index'
    })
  },

  // 结算
  checkout: function () {
    const selectedItems = this.data.cartItems.filter(item => item.selected)

    if (selectedItems.length === 0) {
      wx.showToast({
        title: '请选择商品',
        icon: 'none'
      })
      return
    }

    // 检查登录状态
    if (!app.globalData.token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/user/user'
        })
      }, 1500)
      return
    }

    // 构建订单数据
    const orderItems = selectedItems.map(item => ({
      product_id: item.id,
      quantity: item.quantity
    }))

    console.log('提交订单:', orderItems)

    // 调用创建订单接口
    orderAPI.createOrder({ items: orderItems })
      .then(res => {
        console.log('订单创建成功:', res.data)

        wx.showToast({
          title: '下单成功',
          icon: 'success'
        })

        // 从购物车中移除已下单的商品
        const remainingItems = this.data.cartItems.filter(item => !item.selected)
        this.setData({
          cartItems: remainingItems,
          allSelected: false
        })

        this.updateCart()
        this.calculateTotal()
        this.updateTabBarBadge()

        // 跳转到订单详情或我的页面
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/user/user'
          })
        }, 1500)
      })
      .catch(err => {
        console.error('创建订单失败:', err)
      })
  },

  // 计算总价
  calculateTotal: function () {
    const selectedItems = this.data.cartItems.filter(item => item.selected)
    const priceMode = this.data.priceMode

    let totalAmount = 0
    let saveAmount = 0

    selectedItems.forEach(item => {
      const currentPrice = priceMode === 'wholesale' ? item.price_wholesale : item.price_retail
      const originalPrice = item.price_retail

      totalAmount += currentPrice * item.quantity

      if (priceMode === 'wholesale' && item.price_wholesale < item.price_retail) {
        saveAmount += (originalPrice - item.price_wholesale) * item.quantity
      }
    })

    this.setData({
      selectedCount: selectedItems.reduce((total, item) => total + item.quantity, 0),
      totalAmount: totalAmount.toFixed(2),
      saveAmount: saveAmount.toFixed(2)
    })
  },

  // 更新购物车到全局状态
  updateCart: function () {
    const cart = this.data.cartItems.map(item => ({
      id: item.id,
      name: item.name,
      price_retail: item.price_retail,
      price_wholesale: item.price_wholesale,
      image: item.image,
      quantity: item.quantity
    }))

    app.globalData.cart = cart
    app.saveCart()
  },

  // 更新TabBar徽章
  updateTabBarBadge: function () {
    const count = app.getCartCount()
    if (count > 0) {
      wx.setTabBarBadge({
        index: 2, // 购物车tab的索引
        text: count.toString()
      })
    } else {
      wx.removeTabBarBadge({
        index: 2
      })
    }
  }
})
