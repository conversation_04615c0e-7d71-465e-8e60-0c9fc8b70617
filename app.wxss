/* 刀刀乐捡漏网 - 全局样式 */

/* 全局重置 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'Segoe UI', Arial, Roboto, 'PingFang SC', 'mi<PERSON>', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

/* 通用容器 */
.container {
  padding: 20rpx;
}

.page-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 通用按钮样式 */
.btn {
  display: inline-block;
  padding: 20rpx 40rpx;
  border-radius: 8rpx;
  text-align: center;
  font-size: 28rpx;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: #ff6b35;
  color: white;
}

.btn-primary:active {
  background-color: #e55a2b;
}

.btn-secondary {
  background-color: #f0f0f0;
  color: #666;
}

.btn-secondary:active {
  background-color: #e0e0e0;
}

.btn-small {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.btn-large {
  padding: 28rpx 60rpx;
  font-size: 32rpx;
}

.btn-block {
  width: 100%;
  display: block;
}

/* 卡片样式 */
.card {
  background-color: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
  overflow: hidden;
}

.card-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  font-weight: bold;
}

.card-body {
  padding: 30rpx;
}

.card-footer {
  padding: 20rpx 30rpx;
  background-color: #fafafa;
  border-top: 1rpx solid #f0f0f0;
}

/* 商品卡片 */
.product-card {
  background-color: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 20rpx;
}

.product-image {
  width: 100%;
  height: 300rpx;
  object-fit: cover;
}

.product-info {
  padding: 20rpx;
}

.product-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15rpx;
}

.price-retail {
  color: #ff6b35;
  font-size: 32rpx;
  font-weight: bold;
}

.price-wholesale {
  color: #666;
  font-size: 24rpx;
  text-decoration: line-through;
}

.price-label {
  font-size: 20rpx;
  color: #999;
  margin-right: 8rpx;
}

/* 特价标签 */
.discount-badge {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  background-color: #ff6b35;
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: bold;
}

/* 表单样式 */
.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  margin-bottom: 10rpx;
  font-weight: bold;
  color: #333;
}

.form-input {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: white;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #ff6b35;
}

/* 搜索框 */
.search-box {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 50rpx;
  padding: 15rpx 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.search-input {
  flex: 1;
  border: none;
  font-size: 28rpx;
  background: transparent;
}

.search-btn {
  background-color: #ff6b35;
  color: white;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  margin-left: 20rpx;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 40rpx;
  color: #999;
}

.loading-more {
  text-align: center;
  padding: 20rpx;
  color: #999;
  font-size: 24rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #999;
}

.empty-icon {
  font-size: 100rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  margin-bottom: 30rpx;
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-1 { flex: 1; }
.flex-center { align-items: center; justify-content: center; }
.flex-between { justify-content: space-between; }
.flex-around { justify-content: space-around; }

.mt-10 { margin-top: 10rpx; }
.mt-20 { margin-top: 20rpx; }
.mb-10 { margin-bottom: 10rpx; }
.mb-20 { margin-bottom: 20rpx; }
.ml-10 { margin-left: 10rpx; }
.mr-10 { margin-right: 10rpx; }

.p-10 { padding: 10rpx; }
.p-20 { padding: 20rpx; }
.pt-10 { padding-top: 10rpx; }
.pb-10 { padding-bottom: 10rpx; }

.color-primary { color: #ff6b35; }
.color-success { color: #52c41a; }
.color-warning { color: #faad14; }
.color-danger { color: #f5222d; }
.color-gray { color: #999; }

.font-bold { font-weight: bold; }
.font-small { font-size: 24rpx; }
.font-large { font-size: 32rpx; }
