<!-- 用户页面 -->
<view class="page-container">
  <!-- 未登录状态 -->
  <view class="login-section" wx:if="{{!userInfo}}">
    <view class="login-card card">
      <view class="login-header">
        <view class="login-icon">👤</view>
        <view class="login-title">欢迎来到刀刀乐捡漏网</view>
        <view class="login-desc">登录后享受更多优惠</view>
      </view>
      
      <!-- 登录表单 -->
      <view class="login-form" wx:if="{{!showRegister}}">
        <view class="form-group">
          <input class="form-input" 
                 placeholder="请输入用户名" 
                 value="{{loginForm.username}}"
                 bindinput="onLoginInput"
                 data-field="username" />
        </view>
        <view class="form-group">
          <input class="form-input" 
                 placeholder="请输入密码" 
                 password="true"
                 value="{{loginForm.password}}"
                 bindinput="onLoginInput"
                 data-field="password" />
        </view>
        <view class="form-actions">
          <view class="btn btn-primary btn-block" bindtap="login">登录</view>
          <view class="register-link" bindtap="showRegisterForm">
            还没有账号？立即注册
          </view>
        </view>
      </view>

      <!-- 注册表单 -->
      <view class="register-form" wx:if="{{showRegister}}">
        <view class="form-group">
          <input class="form-input" 
                 placeholder="请输入用户名" 
                 value="{{registerForm.username}}"
                 bindinput="onRegisterInput"
                 data-field="username" />
        </view>
        <view class="form-group">
          <input class="form-input" 
                 placeholder="请输入密码" 
                 password="true"
                 value="{{registerForm.password}}"
                 bindinput="onRegisterInput"
                 data-field="password" />
        </view>
        <view class="form-group">
          <input class="form-input" 
                 placeholder="请输入手机号" 
                 value="{{registerForm.phone}}"
                 bindinput="onRegisterInput"
                 data-field="phone" />
        </view>
        <view class="form-group">
          <view class="user-type-select">
            <text class="form-label">用户类型:</text>
            <view class="type-options">
              <view class="type-option {{registerForm.user_type === 'retail' ? 'active' : ''}}"
                    bindtap="selectUserType"
                    data-type="retail">
                零售用户
              </view>
              <view class="type-option {{registerForm.user_type === 'wholesale' ? 'active' : ''}}"
                    bindtap="selectUserType"
                    data-type="wholesale">
                批发用户
              </view>
            </view>
          </view>
        </view>
        <view class="form-group" wx:if="{{registerForm.user_type === 'wholesale'}}">
          <input class="form-input" 
                 placeholder="请输入营业执照URL" 
                 value="{{registerForm.business_license}}"
                 bindinput="onRegisterInput"
                 data-field="business_license" />
        </view>
        <view class="form-actions">
          <view class="btn btn-primary btn-block" bindtap="register">注册</view>
          <view class="register-link" bindtap="showLoginForm">
            已有账号？立即登录
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 已登录状态 -->
  <view class="user-info-section" wx:if="{{userInfo}}">
    <!-- 用户信息卡片 -->
    <view class="user-info-card card">
      <view class="user-avatar">
        <text class="avatar-text">{{userInfo.username.charAt(0).toUpperCase()}}</text>
      </view>
      <view class="user-details">
        <view class="user-name">{{userInfo.username}}</view>
        <view class="user-type">
          {{userInfo.user_type === 'wholesale' ? '批发用户' : '零售用户'}}
        </view>
      </view>
      <view class="logout-btn" bindtap="logout">退出</view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <!-- 订单相关 -->
      <view class="menu-group card">
        <view class="menu-header">我的订单</view>
        <view class="menu-item" bindtap="goToOrders" data-status="">
          <text class="menu-icon">📋</text>
          <text class="menu-text">全部订单</text>
          <text class="menu-arrow">></text>
        </view>
        <view class="menu-item" bindtap="goToOrders" data-status="pending">
          <text class="menu-icon">⏳</text>
          <text class="menu-text">待付款</text>
          <text class="menu-arrow">></text>
        </view>
        <view class="menu-item" bindtap="goToOrders" data-status="paid">
          <text class="menu-icon">📦</text>
          <text class="menu-text">待发货</text>
          <text class="menu-arrow">></text>
        </view>
      </view>

      <!-- 收藏相关 -->
      <view class="menu-group card">
        <view class="menu-header">我的收藏</view>
        <view class="menu-item" bindtap="goToFavorites">
          <text class="menu-icon">❤️</text>
          <text class="menu-text">收藏的商品</text>
          <text class="menu-arrow">></text>
        </view>
      </view>

      <!-- 设置相关 -->
      <view class="menu-group card">
        <view class="menu-header">设置</view>
        <view class="menu-item" bindtap="toggleMockMode">
          <text class="menu-icon">🔧</text>
          <text class="menu-text">Mock模式</text>
          <text class="menu-status">{{mockMode ? '开启' : '关闭'}}</text>
          <text class="menu-arrow">></text>
        </view>
      </view>
    </view>
  </view>
</view>
