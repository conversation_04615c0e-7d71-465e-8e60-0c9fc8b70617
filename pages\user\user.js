// 用户页面
const { authAPI, orderAPI, favoriteAPI } = require('../../services/api')
const app = getApp()

Page({
  data: {
    // 用户信息
    userInfo: null,
    
    // 显示状态
    showRegister: false,
    
    // 登录表单
    loginForm: {
      username: '',
      password: ''
    },
    
    // 注册表单
    registerForm: {
      username: '',
      password: '',
      phone: '',
      user_type: 'retail',
      business_license: ''
    },
    
    // Mock模式状态
    mockMode: true
  },

  onLoad: function (options) {
    console.log('用户页面加载')
  },

  onShow: function () {
    // 每次显示页面时刷新用户信息
    this.setData({
      userInfo: app.globalData.userInfo,
      mockMode: app.globalData.mock
    })
  },

  onPullDownRefresh: function () {
    console.log('下拉刷新')
    this.setData({
      userInfo: app.globalData.userInfo
    })
    wx.stopPullDownRefresh()
  },

  // 显示注册表单
  showRegisterForm: function () {
    this.setData({
      showRegister: true,
      registerForm: {
        username: '',
        password: '',
        phone: '',
        user_type: 'retail',
        business_license: ''
      }
    })
  },

  // 显示登录表单
  showLoginForm: function () {
    this.setData({
      showRegister: false,
      loginForm: {
        username: '',
        password: ''
      }
    })
  },

  // 登录表单输入
  onLoginInput: function (e) {
    const field = e.currentTarget.dataset.field
    const value = e.detail.value
    
    this.setData({
      [`loginForm.${field}`]: value
    })
  },

  // 注册表单输入
  onRegisterInput: function (e) {
    const field = e.currentTarget.dataset.field
    const value = e.detail.value
    
    this.setData({
      [`registerForm.${field}`]: value
    })
  },

  // 选择用户类型
  selectUserType: function (e) {
    const type = e.currentTarget.dataset.type
    this.setData({
      'registerForm.user_type': type
    })
  },

  // 登录
  login: function () {
    const { username, password } = this.data.loginForm
    
    if (!username.trim()) {
      wx.showToast({
        title: '请输入用户名',
        icon: 'none'
      })
      return
    }
    
    if (!password.trim()) {
      wx.showToast({
        title: '请输入密码',
        icon: 'none'
      })
      return
    }

    console.log('登录:', { username, password })

    authAPI.login({ username: username.trim(), password: password.trim() })
      .then(res => {
        console.log('登录成功:', res.data)
        
        const { user, token } = res.data
        app.setUserInfo(user, token)
        
        this.setData({
          userInfo: user,
          loginForm: {
            username: '',
            password: ''
          }
        })

        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })
      })
      .catch(err => {
        console.error('登录失败:', err)
      })
  },

  // 注册
  register: function () {
    const form = this.data.registerForm
    
    if (!form.username.trim()) {
      wx.showToast({
        title: '请输入用户名',
        icon: 'none'
      })
      return
    }
    
    if (!form.password.trim()) {
      wx.showToast({
        title: '请输入密码',
        icon: 'none'
      })
      return
    }
    
    if (!form.phone.trim()) {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      })
      return
    }
    
    if (form.user_type === 'wholesale' && !form.business_license.trim()) {
      wx.showToast({
        title: '批发用户请输入营业执照',
        icon: 'none'
      })
      return
    }

    const registerData = {
      username: form.username.trim(),
      password: form.password.trim(),
      phone: form.phone.trim(),
      user_type: form.user_type
    }

    if (form.user_type === 'wholesale') {
      registerData.business_license = form.business_license.trim()
    }

    console.log('注册:', registerData)

    authAPI.register(registerData)
      .then(res => {
        console.log('注册成功:', res)
        
        wx.showToast({
          title: '注册成功',
          icon: 'success'
        })

        // 注册成功后自动登录
        setTimeout(() => {
          this.setData({
            showRegister: false,
            loginForm: {
              username: form.username,
              password: form.password
            }
          })
          this.login()
        }, 1500)
      })
      .catch(err => {
        console.error('注册失败:', err)
      })
  },

  // 退出登录
  logout: function () {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          app.clearUserInfo()
          this.setData({
            userInfo: null,
            showRegister: false,
            loginForm: {
              username: '',
              password: ''
            }
          })
          
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          })
        }
      }
    })
  },

  // 跳转订单列表
  goToOrders: function (e) {
    const status = e.currentTarget.dataset.status
    
    if (!app.globalData.token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    // 这里可以跳转到订单列表页面
    // 由于没有创建订单列表页面，暂时显示提示
    wx.showToast({
      title: '订单功能开发中',
      icon: 'none'
    })
  },

  // 跳转收藏列表
  goToFavorites: function () {
    if (!app.globalData.token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    // 这里可以跳转到收藏列表页面
    // 由于没有创建收藏列表页面，暂时显示提示
    wx.showToast({
      title: '收藏功能开发中',
      icon: 'none'
    })
  },

  // 切换Mock模式
  toggleMockMode: function () {
    const newMockMode = !this.data.mockMode
    
    wx.showModal({
      title: '切换Mock模式',
      content: `确定要${newMockMode ? '开启' : '关闭'}Mock模式吗？`,
      success: (res) => {
        if (res.confirm) {
          app.globalData.mock = newMockMode
          this.setData({
            mockMode: newMockMode
          })
          
          wx.showToast({
            title: `Mock模式已${newMockMode ? '开启' : '关闭'}`,
            icon: 'success'
          })
        }
      }
    })
  }
})
