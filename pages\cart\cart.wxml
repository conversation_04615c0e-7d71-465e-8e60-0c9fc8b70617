<!-- 购物车页面 -->
<view class="page-container">
  <!-- 用户类型切换 -->
  <view class="user-type-switch" wx:if="{{userInfo && userInfo.user_type === 'wholesale'}}">
    <view class="switch-container">
      <text class="switch-label">价格模式:</text>
      <view class="switch-options">
        <view class="switch-option {{priceMode === 'retail' ? 'active' : ''}}" 
              bindtap="switchPriceMode" 
              data-mode="retail">
          零售价
        </view>
        <view class="switch-option {{priceMode === 'wholesale' ? 'active' : ''}}" 
              bindtap="switchPriceMode" 
              data-mode="wholesale">
          批发价
        </view>
      </view>
    </view>
  </view>

  <!-- 购物车列表 -->
  <view class="cart-content">
    <!-- 空购物车 -->
    <view class="empty-cart" wx:if="{{cartItems.length === 0}}">
      <view class="empty-icon">🛒</view>
      <view class="empty-text">购物车是空的</view>
      <view class="empty-desc">快去挑选心仪的商品吧</view>
      <view class="btn btn-primary" bindtap="goShopping">去购物</view>
    </view>

    <!-- 购物车商品列表 -->
    <view class="cart-list" wx:if="{{cartItems.length > 0}}">
      <!-- 全选 -->
      <view class="select-all-bar">
        <view class="select-all" bindtap="toggleSelectAll">
          <view class="checkbox {{allSelected ? 'checked' : ''}}">
            <text class="checkbox-icon" wx:if="{{allSelected}}">✓</text>
          </view>
          <text class="select-all-text">全选</text>
        </view>
        <view class="clear-cart" bindtap="clearCart">清空购物车</view>
      </view>

      <!-- 商品列表 -->
      <view class="cart-item" 
            wx:for="{{cartItems}}" 
            wx:key="id">
        
        <!-- 选择框 -->
        <view class="item-select" bindtap="toggleItemSelect" data-index="{{index}}">
          <view class="checkbox {{item.selected ? 'checked' : ''}}">
            <text class="checkbox-icon" wx:if="{{item.selected}}">✓</text>
          </view>
        </view>

        <!-- 商品信息 -->
        <view class="item-info" bindtap="goToDetail" data-id="{{item.id}}">
          <image class="item-image" 
                 src="{{item.image || '/images/placeholder.png'}}" 
                 mode="aspectFill" />
          
          <view class="item-details">
            <view class="item-name">{{item.name}}</view>
            
            <!-- 价格显示 -->
            <view class="item-price">
              <text class="current-price">
                ¥{{priceMode === 'wholesale' ? item.price_wholesale : item.price_retail}}
              </text>
              <text class="original-price" wx:if="{{priceMode === 'wholesale' && item.price_wholesale < item.price_retail}}">
                ¥{{item.price_retail}}
              </text>
            </view>

            <!-- 数量控制 -->
            <view class="quantity-control">
              <view class="quantity-btn" 
                    bindtap="decreaseQuantity" 
                    data-index="{{index}}"
                    disabled="{{item.quantity <= 1}}">
                -
              </view>
              <input class="quantity-input" 
                     type="number" 
                     value="{{item.quantity}}"
                     bindinput="onQuantityInput"
                     data-index="{{index}}" />
              <view class="quantity-btn" 
                    bindtap="increaseQuantity" 
                    data-index="{{index}}">
                +
              </view>
            </view>
          </view>
        </view>

        <!-- 删除按钮 -->
        <view class="item-delete" bindtap="removeItem" data-index="{{index}}">
          <text class="delete-icon">🗑️</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部结算栏 -->
  <view class="checkout-bar" wx:if="{{cartItems.length > 0}}">
    <view class="checkout-info">
      <view class="selected-count">
        已选择 {{selectedCount}} 件商品
      </view>
      <view class="total-price">
        <text class="total-label">合计:</text>
        <text class="total-amount">¥{{totalAmount}}</text>
      </view>
      <view class="save-amount" wx:if="{{saveAmount > 0}}">
        已省¥{{saveAmount}}
      </view>
    </view>
    
    <view class="checkout-btn {{selectedCount > 0 ? 'active' : ''}}" 
          bindtap="checkout">
      结算 ({{selectedCount}})
    </view>
  </view>
</view>
