// 商品详情页
const { productAPI, favoriteAPI } = require('../../services/api')
const app = getApp()

Page({
  data: {
    // 商品信息
    product: null,
    productId: null,
    
    // 用户信息
    userInfo: null,
    
    // 收藏状态
    isFavorited: false,
    favoriteId: null,
    
    // 购物车数量
    cartCount: 0,
    
    // 加载状态
    loading: true
  },

  onLoad: function (options) {
    const productId = options.id
    console.log('商品详情页加载, ID:', productId)
    
    if (!productId) {
      wx.showToast({
        title: '商品ID错误',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    this.setData({
      productId: parseInt(productId),
      userInfo: app.globalData.userInfo
    })

    this.loadProductDetail()
    this.updateCartCount()
  },

  onShow: function () {
    // 每次显示页面时更新购物车数量和用户信息
    this.setData({
      userInfo: app.globalData.userInfo
    })
    this.updateCartCount()
  },

  // 加载商品详情
  loadProductDetail: function () {
    this.setData({ loading: true })

    productAPI.fetchProductDetail(this.data.productId)
      .then(res => {
        const product = res.data
        this.setData({
          product,
          loading: false
        })
        
        // 设置页面标题
        wx.setNavigationBarTitle({
          title: product.name || '商品详情'
        })

        // 检查收藏状态
        this.checkFavoriteStatus()
      })
      .catch(err => {
        console.error('加载商品详情失败:', err)
        this.setData({
          loading: false
        })
      })
  },

  // 检查收藏状态
  checkFavoriteStatus: function () {
    if (!app.globalData.token) {
      return
    }

    favoriteAPI.fetchFavorites()
      .then(res => {
        const favorites = res.data || []
        const favorite = favorites.find(item => item.product_id === this.data.productId)
        
        this.setData({
          isFavorited: !!favorite,
          favoriteId: favorite ? favorite.id : null
        })
      })
      .catch(err => {
        console.error('检查收藏状态失败:', err)
      })
  },

  // 图片预览
  previewImage: function (e) {
    const url = e.currentTarget.dataset.url
    const urls = []
    
    // 收集所有图片URL
    if (this.data.product.image) {
      urls.push(this.data.product.image)
    }
    if (this.data.product.images && this.data.product.images.length > 0) {
      urls.push(...this.data.product.images)
    }

    wx.previewImage({
      current: url,
      urls: urls
    })
  },

  // 切换收藏状态
  toggleFavorite: function () {
    if (!app.globalData.token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/user/user'
        })
      }, 1500)
      return
    }

    if (this.data.isFavorited) {
      // 取消收藏
      this.removeFavorite()
    } else {
      // 添加收藏
      this.addFavorite()
    }
  },

  // 添加收藏
  addFavorite: function () {
    favoriteAPI.addFavorite({
      product_id: this.data.productId
    })
      .then(res => {
        this.setData({
          isFavorited: true
        })
        wx.showToast({
          title: '收藏成功',
          icon: 'success'
        })
        // 重新获取收藏状态以获取favoriteId
        this.checkFavoriteStatus()
      })
      .catch(err => {
        console.error('添加收藏失败:', err)
      })
  },

  // 取消收藏
  removeFavorite: function () {
    if (!this.data.favoriteId) {
      return
    }

    favoriteAPI.deleteFavorite(this.data.favoriteId)
      .then(res => {
        this.setData({
          isFavorited: false,
          favoriteId: null
        })
        wx.showToast({
          title: '取消收藏',
          icon: 'success'
        })
      })
      .catch(err => {
        console.error('取消收藏失败:', err)
      })
  },

  // 加入购物车
  addToCart: function () {
    const product = this.data.product
    
    if (!product) {
      return
    }

    if (product.stock <= 0) {
      wx.showToast({
        title: '商品已售罄',
        icon: 'none'
      })
      return
    }

    app.addToCart(product)
    this.updateCartCount()
  },

  // 立即购买
  buyNow: function () {
    const product = this.data.product
    
    if (!product) {
      return
    }

    if (product.stock <= 0) {
      wx.showToast({
        title: '商品已售罄',
        icon: 'none'
      })
      return
    }

    // 先加入购物车
    app.addToCart(product)
    
    // 跳转到购物车页面
    wx.switchTab({
      url: '/pages/cart/cart'
    })
  },

  // 跳转购物车
  goToCart: function () {
    wx.switchTab({
      url: '/pages/cart/cart'
    })
  },

  // 返回
  goBack: function () {
    wx.navigateBack()
  },

  // 更新购物车数量
  updateCartCount: function () {
    const count = app.getCartCount()
    this.setData({
      cartCount: count
    })
    
    if (count > 0) {
      wx.setTabBarBadge({
        index: 2, // 购物车tab的索引
        text: count.toString()
      })
    } else {
      wx.removeTabBarBadge({
        index: 2
      })
    }
  }
})
