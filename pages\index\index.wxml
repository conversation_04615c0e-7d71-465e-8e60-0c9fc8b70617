<!-- 首页 - 商品列表 -->
<view class="page-container">
  <!-- 搜索栏 -->
  <view class="search-container">
    <view class="search-box">
      <input 
        class="search-input" 
        placeholder="搜索商品..." 
        value="{{keyword}}"
        bindinput="onSearchInput"
        confirm-type="search"
        bindconfirm="onSearch"
      />
      <view class="search-btn" bindtap="onSearch">搜索</view>
    </view>
  </view>

  <!-- 分类筛选 -->
  <view class="category-filter" wx:if="{{categories.length > 0}}">
    <scroll-view class="category-scroll" scroll-x="true">
      <view class="category-item {{selectedCategoryId === 0 ? 'active' : ''}}" 
            bindtap="onCategorySelect" 
            data-id="0">
        全部
      </view>
      <view class="category-item {{selectedCategoryId === item.id ? 'active' : ''}}" 
            wx:for="{{categories}}" 
            wx:key="id"
            bindtap="onCategorySelect" 
            data-id="{{item.id}}">
        {{item.name}}
      </view>
    </scroll-view>
  </view>

  <!-- 商品列表 -->
  <view class="product-list">
    <!-- 加载状态 -->
    <view class="loading" wx:if="{{loading && products.length === 0}}">
      <text>加载中...</text>
    </view>

    <!-- 商品网格 -->
    <view class="product-grid" wx:if="{{products.length > 0}}">
      <view class="product-card" 
            wx:for="{{products}}" 
            wx:key="id"
            bindtap="goToDetail"
            data-id="{{item.id}}">
        
        <!-- 商品图片 -->
        <view class="product-image-container">
          <image class="product-image" 
                 src="{{item.image || '/images/placeholder.png'}}" 
                 mode="aspectFill"
                 lazy-load="true" />
          <!-- 特价标签 -->
          <view class="discount-badge" wx:if="{{item.price_wholesale < item.price_retail}}">
            特价
          </view>
        </view>

        <!-- 商品信息 -->
        <view class="product-info">
          <view class="product-title">{{item.name}}</view>
          
          <!-- 价格信息 -->
          <view class="product-price">
            <view class="price-main">
              <text class="price-label">零售价</text>
              <text class="price-retail">¥{{item.price_retail}}</text>
            </view>
            <view class="price-wholesale" wx:if="{{item.price_wholesale < item.price_retail}}">
              <text class="price-label">批发价</text>
              <text class="price-value">¥{{item.price_wholesale}}</text>
            </view>
          </view>

          <!-- 库存信息 -->
          <view class="product-stock">
            <text class="stock-text">库存: {{item.stock}}</text>
          </view>

          <!-- 操作按钮 -->
          <view class="product-actions">
            <view class="btn btn-secondary btn-small" 
                  bindtap="goToDetail" 
                  data-id="{{item.id}}"
                  catchtap="true">
              查看详情
            </view>
            <view class="btn btn-primary btn-small" 
                  bindtap="addToCart" 
                  data-product="{{item}}"
                  catchtap="true">
              加入购物车
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && products.length === 0}}">
      <view class="empty-icon">📦</view>
      <view class="empty-text">暂无商品</view>
      <view class="btn btn-primary" bindtap="onRefresh">刷新</view>
    </view>

    <!-- 加载更多 -->
    <view class="loading-more" wx:if="{{hasMore && products.length > 0}}">
      <text wx:if="{{loadingMore}}">加载中...</text>
      <text wx:else>上拉加载更多</text>
    </view>

    <!-- 没有更多 -->
    <view class="loading-more" wx:if="{{!hasMore && products.length > 0}}">
      <text>没有更多商品了</text>
    </view>
  </view>
</view>
