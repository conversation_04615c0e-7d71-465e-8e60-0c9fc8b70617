/* 首页样式 */

.page-container {
  padding-bottom: 20rpx;
}

/* 搜索容器 */
.search-container {
  padding: 20rpx;
  background-color: white;
  margin-bottom: 20rpx;
}

/* 分类筛选 */
.category-filter {
  background-color: white;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
}

.category-scroll {
  white-space: nowrap;
  padding: 0 20rpx;
}

.category-item {
  display: inline-block;
  padding: 12rpx 24rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
}

.category-item.active {
  background-color: #ff6b35;
  color: white;
}

/* 商品列表 */
.product-list {
  padding: 0 20rpx;
}

.product-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.product-card {
  width: 340rpx;
  background-color: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
  overflow: hidden;
  transition: transform 0.2s ease;
}

.product-card:active {
  transform: scale(0.98);
}

/* 商品图片 */
.product-image-container {
  position: relative;
  width: 100%;
  height: 240rpx;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.discount-badge {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  background-color: #ff6b35;
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: bold;
}

/* 商品信息 */
.product-info {
  padding: 20rpx;
}

.product-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 价格信息 */
.product-price {
  margin-bottom: 12rpx;
}

.price-main {
  display: flex;
  align-items: center;
  margin-bottom: 6rpx;
}

.price-label {
  font-size: 20rpx;
  color: #999;
  margin-right: 8rpx;
}

.price-retail {
  color: #ff6b35;
  font-size: 30rpx;
  font-weight: bold;
}

.price-wholesale {
  display: flex;
  align-items: center;
}

.price-wholesale .price-value {
  color: #52c41a;
  font-size: 24rpx;
  font-weight: bold;
}

/* 库存信息 */
.product-stock {
  margin-bottom: 15rpx;
}

.stock-text {
  font-size: 22rpx;
  color: #999;
}

/* 操作按钮 */
.product-actions {
  display: flex;
  justify-content: space-between;
  gap: 10rpx;
}

.product-actions .btn {
  flex: 1;
  text-align: center;
  padding: 12rpx 0;
  font-size: 24rpx;
}

/* 响应式布局 */
@media screen and (max-width: 375px) {
  .product-card {
    width: 320rpx;
  }
}

@media screen and (min-width: 414px) {
  .product-card {
    width: 360rpx;
  }
}
