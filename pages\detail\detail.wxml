<!-- 商品详情页 -->
<view class="page-container">
  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>

  <!-- 商品详情内容 -->
  <view class="detail-content" wx:if="{{!loading && product}}">
    <!-- 商品图片轮播 -->
    <view class="product-images">
      <swiper class="image-swiper" 
              indicator-dots="{{product.images && product.images.length > 1}}"
              indicator-color="rgba(255,255,255,0.5)"
              indicator-active-color="#ff6b35"
              autoplay="false"
              circular="true">
        <swiper-item wx:if="{{product.image}}">
          <image class="product-image" 
                 src="{{product.image}}" 
                 mode="aspectFill"
                 bindtap="previewImage"
                 data-url="{{product.image}}" />
        </swiper-item>
        <swiper-item wx:for="{{product.images}}" wx:key="*this" wx:if="{{product.images && product.images.length > 0}}">
          <image class="product-image" 
                 src="{{item}}" 
                 mode="aspectFill"
                 bindtap="previewImage"
                 data-url="{{item}}" />
        </swiper-item>
      </swiper>
      
      <!-- 特价标签 -->
      <view class="discount-badge" wx:if="{{product.price_wholesale < product.price_retail}}">
        特价捡漏
      </view>
    </view>

    <!-- 商品基本信息 -->
    <view class="product-basic-info card">
      <view class="product-title">{{product.name}}</view>
      
      <!-- 价格信息 -->
      <view class="price-section">
        <view class="price-row">
          <text class="price-label">零售价</text>
          <text class="price-retail">¥{{product.price_retail}}</text>
        </view>
        <view class="price-row" wx:if="{{product.price_wholesale < product.price_retail}}">
          <text class="price-label">批发价</text>
          <text class="price-wholesale">¥{{product.price_wholesale}}</text>
          <text class="save-amount">省¥{{product.price_retail - product.price_wholesale}}</text>
        </view>
      </view>

      <!-- 库存信息 -->
      <view class="stock-info">
        <text class="stock-label">库存:</text>
        <text class="stock-value {{product.stock <= 10 ? 'low-stock' : ''}}">
          {{product.stock}}件
        </text>
        <text class="low-stock-tip" wx:if="{{product.stock <= 10 && product.stock > 0}}">
          仅剩{{product.stock}}件，抓紧抢购！
        </text>
        <text class="out-of-stock" wx:if="{{product.stock <= 0}}">
          已售罄
        </text>
      </view>
    </view>

    <!-- 商品描述 -->
    <view class="product-description card">
      <view class="card-header">商品详情</view>
      <view class="card-body">
        <text class="description-text">{{product.description || '暂无详细描述'}}</text>
      </view>
    </view>

    <!-- 用户类型提示 -->
    <view class="user-type-tip card" wx:if="{{userInfo}}">
      <view class="tip-content">
        <text class="tip-icon">👤</text>
        <text class="tip-text">
          您是{{userInfo.user_type === 'wholesale' ? '批发' : '零售'}}用户，
          {{userInfo.user_type === 'wholesale' ? '享受批发价优惠' : '按零售价结算'}}
        </text>
      </view>
    </view>
  </view>

  <!-- 错误状态 -->
  <view class="error-state" wx:if="{{!loading && !product}}">
    <view class="error-icon">❌</view>
    <view class="error-text">商品不存在或已下架</view>
    <view class="btn btn-primary" bindtap="goBack">返回</view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions" wx:if="{{product}}">
    <view class="action-left">
      <view class="action-btn" bindtap="toggleFavorite">
        <text class="action-icon">{{isFavorited ? '❤️' : '🤍'}}</text>
        <text class="action-text">{{isFavorited ? '已收藏' : '收藏'}}</text>
      </view>
      <view class="action-btn" bindtap="goToCart">
        <text class="action-icon">🛒</text>
        <text class="action-text">购物车</text>
        <view class="cart-badge" wx:if="{{cartCount > 0}}">{{cartCount}}</view>
      </view>
    </view>
    
    <view class="action-right">
      <view class="btn btn-secondary" 
            bindtap="addToCart"
            disabled="{{product.stock <= 0}}">
        加入购物车
      </view>
      <view class="btn btn-primary" 
            bindtap="buyNow"
            disabled="{{product.stock <= 0}}">
        立即购买
      </view>
    </view>
  </view>
</view>
