// 刀刀乐捡漏网 - 微信小程序
App({
  globalData: {
    // 基础配置
    baseUrl: 'https://api.daodaole.com', // 生产环境API地址
    mock: true, // 是否开启mock模式，true=使用本地mock数据，false=调用真实接口
    
    // 用户信息
    userInfo: null,
    token: null,
    
    // 购物车数据 (本地存储)
    cart: []
  },

  onLaunch: function () {
    console.log('刀刀乐捡漏网启动')
    
    // 初始化用户信息
    this.initUserInfo()
    
    // 初始化购物车
    this.initCart()
    
    // 检查更新
    this.checkUpdate()
  },

  // 初始化用户信息
  initUserInfo: function() {
    try {
      const token = wx.getStorageSync('token')
      const user = wx.getStorageSync('user')
      
      if (token && user) {
        this.globalData.token = token
        this.globalData.userInfo = user
        console.log('用户已登录:', user.username)
      }
    } catch (e) {
      console.error('初始化用户信息失败:', e)
    }
  },

  // 初始化购物车
  initCart: function() {
    try {
      const cart = wx.getStorageSync('cart')
      if (cart && Array.isArray(cart)) {
        this.globalData.cart = cart
      }
    } catch (e) {
      console.error('初始化购物车失败:', e)
    }
  },

  // 检查小程序更新
  checkUpdate: function() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager()
      
      updateManager.onCheckForUpdate(function (res) {
        if (res.hasUpdate) {
          console.log('发现新版本')
        }
      })

      updateManager.onUpdateReady(function () {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: function (res) {
            if (res.confirm) {
              updateManager.applyUpdate()
            }
          }
        })
      })

      updateManager.onUpdateFailed(function () {
        console.error('新版本下载失败')
      })
    }
  },

  // 设置用户信息
  setUserInfo: function(userInfo, token) {
    this.globalData.userInfo = userInfo
    this.globalData.token = token
    
    // 保存到本地存储
    try {
      wx.setStorageSync('user', userInfo)
      wx.setStorageSync('token', token)
    } catch (e) {
      console.error('保存用户信息失败:', e)
    }
  },

  // 清除用户信息（退出登录）
  clearUserInfo: function() {
    this.globalData.userInfo = null
    this.globalData.token = null
    
    try {
      wx.removeStorageSync('user')
      wx.removeStorageSync('token')
    } catch (e) {
      console.error('清除用户信息失败:', e)
    }
  },

  // 添加商品到购物车
  addToCart: function(product) {
    const cart = this.globalData.cart
    const existIndex = cart.findIndex(item => item.id === product.id)
    
    if (existIndex >= 0) {
      // 商品已存在，增加数量
      cart[existIndex].quantity += 1
    } else {
      // 新商品，添加到购物车
      cart.push({
        id: product.id,
        name: product.name,
        price_retail: product.price_retail,
        price_wholesale: product.price_wholesale,
        image: product.image || '',
        quantity: 1
      })
    }
    
    this.globalData.cart = cart
    this.saveCart()
    
    wx.showToast({
      title: '已加入购物车',
      icon: 'success'
    })
  },

  // 保存购物车到本地存储
  saveCart: function() {
    try {
      wx.setStorageSync('cart', this.globalData.cart)
    } catch (e) {
      console.error('保存购物车失败:', e)
    }
  },

  // 清空购物车
  clearCart: function() {
    this.globalData.cart = []
    this.saveCart()
  },

  // 获取购物车商品数量
  getCartCount: function() {
    return this.globalData.cart.reduce((total, item) => total + item.quantity, 0)
  }
})
