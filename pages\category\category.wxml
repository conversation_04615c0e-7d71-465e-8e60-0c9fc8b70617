<!-- 分类页面 -->
<view class="page-container">
  <view class="category-container">
    <!-- 左侧分类列表 -->
    <view class="category-sidebar">
      <view class="category-item {{selectedCategoryId === item.id ? 'active' : ''}}"
            wx:for="{{categories}}"
            wx:key="id"
            bindtap="onCategorySelect"
            data-id="{{item.id}}">
        <text class="category-name">{{item.name}}</text>
        <view class="category-arrow" wx:if="{{selectedCategoryId === item.id}}">></view>
      </view>
    </view>

    <!-- 右侧商品列表 -->
    <view class="product-content">
      <!-- 加载状态 -->
      <view class="loading" wx:if="{{loading}}">
        <text>加载中...</text>
      </view>

      <!-- 商品列表 -->
      <view class="product-list" wx:if="{{!loading && products.length > 0}}">
        <view class="product-item"
              wx:for="{{products}}"
              wx:key="id"
              bindtap="goToDetail"
              data-id="{{item.id}}">
          
          <!-- 商品图片 -->
          <view class="product-image-container">
            <image class="product-image" 
                   src="{{item.image || '/images/placeholder.png'}}" 
                   mode="aspectFill"
                   lazy-load="true" />
            <!-- 特价标签 -->
            <view class="discount-badge" wx:if="{{item.price_wholesale < item.price_retail}}">
              特价
            </view>
          </view>

          <!-- 商品信息 -->
          <view class="product-info">
            <view class="product-title">{{item.name}}</view>
            
            <!-- 价格信息 -->
            <view class="product-price">
              <view class="price-retail">¥{{item.price_retail}}</view>
              <view class="price-wholesale" wx:if="{{item.price_wholesale < item.price_retail}}">
                批发价: ¥{{item.price_wholesale}}
              </view>
            </view>

            <!-- 库存 -->
            <view class="product-stock">库存: {{item.stock}}</view>

            <!-- 加入购物车按钮 -->
            <view class="add-cart-btn" 
                  bindtap="addToCart" 
                  data-product="{{item}}"
                  catchtap="true">
              加入购物车
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{!loading && products.length === 0}}">
        <view class="empty-icon">📦</view>
        <view class="empty-text">该分类暂无商品</view>
      </view>

      <!-- 加载更多 -->
      <view class="loading-more" wx:if="{{hasMore && products.length > 0}}">
        <text wx:if="{{loadingMore}}">加载中...</text>
        <text wx:else>上拉加载更多</text>
      </view>

      <!-- 没有更多 -->
      <view class="loading-more" wx:if="{{!hasMore && products.length > 0}}">
        <text>没有更多商品了</text>
      </view>
    </view>
  </view>
</view>
