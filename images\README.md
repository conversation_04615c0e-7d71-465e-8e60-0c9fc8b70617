# 图片资源说明

本目录需要放置以下图片资源：

## TabBar图标（必需）

请准备以下8个图标文件，建议尺寸：81px × 81px

### 首页图标
- `home.png` - 首页图标（未选中状态）
- `home-active.png` - 首页图标（选中状态，建议橙色 #ff6b35）

### 分类图标  
- `category.png` - 分类图标（未选中状态）
- `category-active.png` - 分类图标（选中状态，建议橙色 #ff6b35）

### 购物车图标
- `cart.png` - 购物车图标（未选中状态）
- `cart-active.png` - 购物车图标（选中状态，建议橙色 #ff6b35）

### 用户图标
- `user.png` - 用户图标（未选中状态）
- `user-active.png` - 用户图标（选中状态，建议橙色 #ff6b35）

## 其他图标

### 占位图
- `placeholder.png` - 商品图片占位图，建议尺寸：300px × 300px

## 图标获取方式

### 方式1：阿里Iconfont（推荐）

1. 访问 https://www.iconfont.cn/
2. 搜索关键词：
   - 首页：home、房子、主页
   - 分类：category、分类、菜单
   - 购物车：cart、购物车、shopping
   - 用户：user、用户、个人中心
3. 选择合适的图标下载PNG格式
4. 调整为所需尺寸和颜色

### 方式2：其他图标库

- IconFont: https://www.iconfont.cn/
- Feather Icons: https://feathericons.com/
- Heroicons: https://heroicons.com/
- Tabler Icons: https://tabler-icons.io/

### 方式3：设计工具制作

使用Figma、Sketch、Adobe Illustrator等工具自行设计

## 颜色规范

- **主色调**：#ff6b35（橙色）
- **未选中状态**：#666666（灰色）
- **选中状态**：#ff6b35（橙色）

## 文件命名规范

请严格按照以下命名，否则可能导致图标显示异常：

```
home.png
home-active.png
category.png
category-active.png
cart.png
cart-active.png
user.png
user-active.png
placeholder.png
```

## 临时解决方案

如果暂时没有图标资源，可以：

1. 使用emoji表情符号替代（已在代码中实现）
2. 使用纯色块作为临时图标
3. 从免费图标库下载基础图标

## 注意事项

1. 图标文件大小建议控制在20KB以内
2. 使用PNG格式以支持透明背景
3. 确保图标在不同设备上显示清晰
4. 遵循微信小程序设计规范
