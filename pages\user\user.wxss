/* 用户页面样式 */

.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 20rpx;
}

/* 登录区域 */
.login-section {
  padding-top: 100rpx;
}

.login-card {
  padding: 60rpx 40rpx;
  text-align: center;
}

.login-header {
  margin-bottom: 60rpx;
}

.login-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.login-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.login-desc {
  font-size: 26rpx;
  color: #666;
}

/* 表单样式 */
.login-form,
.register-form {
  text-align: left;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-input {
  width: 100%;
  padding: 25rpx 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #f9f9f9;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #ff6b35;
  background-color: white;
}

.form-actions {
  margin-top: 40rpx;
  text-align: center;
}

.register-link {
  margin-top: 30rpx;
  font-size: 26rpx;
  color: #ff6b35;
  text-decoration: underline;
}

/* 用户类型选择 */
.user-type-select {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.type-options {
  display: flex;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  padding: 4rpx;
}

.type-option {
  padding: 12rpx 30rpx;
  border-radius: 26rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
  margin-left: 8rpx;
}

.type-option.active {
  background-color: #ff6b35;
  color: white;
}

/* 用户信息区域 */
.user-info-section {
  padding-top: 20rpx;
}

.user-info-card {
  display: flex;
  align-items: center;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: linear-gradient(45deg, #ff6b35, #ff8c42);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
}

.avatar-text {
  font-size: 40rpx;
  color: white;
  font-weight: bold;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.user-type {
  font-size: 24rpx;
  color: #666;
  background-color: #f0f0f0;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  display: inline-block;
}

.logout-btn {
  font-size: 26rpx;
  color: #999;
  padding: 10rpx 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.logout-btn:active {
  background-color: #f5f5f5;
}

/* 菜单区域 */
.menu-section {
  margin-top: 20rpx;
}

.menu-group {
  margin-bottom: 20rpx;
  padding: 0;
  overflow: hidden;
}

.menu-header {
  padding: 25rpx 30rpx;
  background-color: #f8f8f8;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  border-bottom: 1rpx solid #e5e5e5;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f5f5f5;
}

.menu-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  width: 40rpx;
  text-align: center;
}

.menu-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.menu-status {
  font-size: 24rpx;
  color: #666;
  margin-right: 10rpx;
}

.menu-arrow {
  font-size: 24rpx;
  color: #ccc;
}
