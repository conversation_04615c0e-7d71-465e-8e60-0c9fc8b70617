/* 分类页面样式 */

.page-container {
  height: 100vh;
  background-color: #f5f5f5;
}

.category-container {
  display: flex;
  height: 100%;
}

/* 左侧分类列表 */
.category-sidebar {
  width: 200rpx;
  background-color: #f8f8f8;
  border-right: 1rpx solid #e5e5e5;
}

.category-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #e5e5e5;
  background-color: #f8f8f8;
  transition: all 0.3s ease;
}

.category-item.active {
  background-color: white;
  border-right: 4rpx solid #ff6b35;
}

.category-item:active {
  background-color: #e5e5e5;
}

.category-name {
  font-size: 26rpx;
  color: #333;
  text-align: center;
  flex: 1;
}

.category-item.active .category-name {
  color: #ff6b35;
  font-weight: bold;
}

.category-arrow {
  font-size: 24rpx;
  color: #ff6b35;
  font-weight: bold;
}

/* 右侧商品内容 */
.product-content {
  flex: 1;
  background-color: white;
  overflow-y: auto;
}

.product-list {
  padding: 20rpx;
}

.product-item {
  display: flex;
  background-color: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
  overflow: hidden;
  transition: transform 0.2s ease;
}

.product-item:active {
  transform: scale(0.98);
}

/* 商品图片 */
.product-image-container {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  flex-shrink: 0;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.discount-badge {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  background-color: #ff6b35;
  color: white;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  font-size: 18rpx;
  font-weight: bold;
}

/* 商品信息 */
.product-info {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 价格信息 */
.product-price {
  margin-bottom: 10rpx;
}

.price-retail {
  color: #ff6b35;
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 5rpx;
}

.price-wholesale {
  color: #52c41a;
  font-size: 22rpx;
}

/* 库存信息 */
.product-stock {
  font-size: 22rpx;
  color: #999;
  margin-bottom: 15rpx;
}

/* 加入购物车按钮 */
.add-cart-btn {
  background-color: #ff6b35;
  color: white;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  text-align: center;
  align-self: flex-start;
  transition: background-color 0.3s ease;
}

.add-cart-btn:active {
  background-color: #e55a2b;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #999;
}

.empty-icon {
  font-size: 100rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
}

/* 加载更多 */
.loading-more {
  text-align: center;
  padding: 20rpx;
  color: #999;
  font-size: 24rpx;
}
